[{"D:\\MobioProjects\\iot-poc\\frontend\\src\\index.js": "1", "D:\\MobioProjects\\iot-poc\\frontend\\src\\App.js": "2", "D:\\MobioProjects\\iot-poc\\frontend\\src\\reportWebVitals.js": "3", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\Chart.js": "4", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\EnvironmentalChart.js": "5", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\MotionLightChart.js": "6", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\PressureFlowChart.js": "7", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\AirQualityChart.js": "8", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\AlertSystem.js": "9", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\LubricantChart.js": "10"}, {"size": 535, "mtime": 1750655226004, "results": "11", "hashOfConfig": "12"}, {"size": 6186, "mtime": 1750753189551, "results": "13", "hashOfConfig": "12"}, {"size": 362, "mtime": 1750655243006, "results": "14", "hashOfConfig": "12"}, {"size": 2656, "mtime": 1750656459757, "results": "15", "hashOfConfig": "12"}, {"size": 2919, "mtime": 1750657499495, "results": "16", "hashOfConfig": "12"}, {"size": 3753, "mtime": 1750657545258, "results": "17", "hashOfConfig": "12"}, {"size": 4347, "mtime": 1750657570600, "results": "18", "hashOfConfig": "12"}, {"size": 3421, "mtime": 1750657522158, "results": "19", "hashOfConfig": "12"}, {"size": 4493, "mtime": 1750753025532, "results": "20", "hashOfConfig": "12"}, {"size": 6312, "mtime": 1750666935044, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "vgru56", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\MobioProjects\\iot-poc\\frontend\\src\\index.js", [], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\App.js", ["52", "53"], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\reportWebVitals.js", [], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\Chart.js", [], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\EnvironmentalChart.js", [], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\MotionLightChart.js", ["54", "55", "56"], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\PressureFlowChart.js", ["57", "58"], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\AirQualityChart.js", ["59"], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\AlertSystem.js", [], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\LubricantChart.js", [], [], {"ruleId": "60", "severity": 1, "message": "61", "line": 9, "column": 8, "nodeType": "62", "messageId": "63", "endLine": 9, "endColumn": 19}, {"ruleId": "64", "severity": 1, "message": "65", "line": 56, "column": 6, "nodeType": "66", "endLine": 56, "endColumn": 8, "suggestions": "67"}, {"ruleId": "60", "severity": 1, "message": "68", "line": 2, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 2, "endColumn": 12}, {"ruleId": "60", "severity": 1, "message": "69", "line": 12, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 12, "endColumn": 15}, {"ruleId": "60", "severity": 1, "message": "70", "line": 13, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 13, "endColumn": 10}, {"ruleId": "60", "severity": 1, "message": "68", "line": 2, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 2, "endColumn": 12}, {"ruleId": "60", "severity": 1, "message": "71", "line": 11, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 11, "endColumn": 6}, {"ruleId": "60", "severity": 1, "message": "68", "line": 2, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 2, "endColumn": 12}, "no-unused-vars", "'AlertSystem' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["72"], "'LineChart' is defined but never used.", "'ScatterChart' is defined but never used.", "'Scatter' is defined but never used.", "'Bar' is defined but never used.", {"desc": "73", "fix": "74"}, "Update the dependencies array to be: [fetchData]", {"range": "75", "text": "76"}, [1994, 1996], "[fetchData]"]