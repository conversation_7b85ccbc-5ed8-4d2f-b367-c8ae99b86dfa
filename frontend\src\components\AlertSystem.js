import React, { useEffect, useRef } from "react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const AlertSystem = ({ data }) => {
  const lastAlertTime = useRef({});
  const ALERT_COOLDOWN = 30000; // 30 seconds cooldown between alerts for same sensor

  useEffect(() => {
    // Check for lubricant level alerts
    const lubricantData = data.filter(
      (item) => item.sensor_type === "lubrication_level"
    );

    lubricantData.forEach((reading) => {
      if (reading.lubricant_level > 60) {
        const now = Date.now();
        const lastAlert = lastAlertTime.current[reading.device_id] || 0;

        // Only show alert if cooldown period has passed
        if (now - lastAlert > ALERT_COOLDOWN) {
          showLubricantAlert(reading);
          lastAlertTime.current[reading.device_id] = now;
        }
      }
    });
  }, [data]);

  const showLubricantAlert = (reading) => {
    const alertLevel = reading.lubricant_level > 80 ? "CRITICAL" : "WARNING";
    const alertColor = reading.lubricant_level > 80 ? "#F44336" : "#FF9800";
    const alertIcon = reading.lubricant_level > 80 ? "🚨" : "⚠️";

    const AlertContent = () => (
      <div
        style={{
          display: "flex",
          alignItems: "flex-start",
          gap: "12px",
          padding: "8px 4px",
        }}
      >
        <div
          style={{
            fontSize: "28px",
            lineHeight: "1",
            marginTop: "2px",
          }}
        >
          {alertIcon}
        </div>
        <div style={{ flex: 1 }}>
          <div
            style={{
              fontWeight: "bold",
              fontSize: "16px",
              color: alertColor,
              marginBottom: "6px",
              lineHeight: "1.2",
            }}
          >
            {alertLevel} ALERT
          </div>
          <div
            style={{
              fontSize: "15px",
              fontWeight: "600",
              color: "#333",
              marginBottom: "4px",
            }}
          >
            High Lubricant Level Detected
          </div>
          <div
            style={{
              fontSize: "14px",
              color: "#555",
              marginBottom: "6px",
            }}
          >
            <strong>{reading.device_id}</strong>: {reading.lubricant_level}%
          </div>
          <div
            style={{
              fontSize: "12px",
              color: "#777",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <span>📍 {reading.location}</span>
            <span
              style={{
                backgroundColor: alertColor,
                color: "white",
                padding: "2px 6px",
                borderRadius: "10px",
                fontSize: "11px",
                fontWeight: "bold",
              }}
            >
              Threshold: 60%
            </span>
          </div>
        </div>
      </div>
    );

    toast.error(<AlertContent />, {
      position: "top-right",
      autoClose: reading.lubricant_level > 80 ? false : 10000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      className: `lubricant-alert-toast ${
        reading.lubricant_level > 80 ? "critical-alert" : ""
      }`,
      bodyClassName: "lubricant-alert-body",
      progressClassName: "lubricant-alert-progress",
      style: {
        backgroundColor: "#ffffff",
        border: `3px solid ${alertColor}`,
        borderRadius: "12px",
        boxShadow:
          reading.lubricant_level > 80
            ? `0 8px 32px rgba(244, 67, 54, 0.2), 0 0 0 1px ${alertColor}30`
            : `0 8px 32px rgba(0,0,0,0.12), 0 0 0 1px ${alertColor}20`,
        minHeight: "80px",
        padding: "0",
      },
      toastId: `lubricant-${reading.device_id}-${Math.floor(
        reading.lubricant_level / 10
      )}`,
    });

    // Also log to console for debugging
    console.warn(
      `LUBRICANT ALERT: ${reading.device_id} - Level: ${reading.lubricant_level}%`
    );
  };

  return (
    <ToastContainer
      position="top-right"
      autoClose={5000}
      hideProgressBar={false}
      newestOnTop={true}
      closeOnClick
      rtl={false}
      pauseOnFocusLoss
      draggable
      pauseOnHover
      theme="light"
      // style={{ zIndex: 9999 }}
    />
  );
};

export default AlertSystem;
