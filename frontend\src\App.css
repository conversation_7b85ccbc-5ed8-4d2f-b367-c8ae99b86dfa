.dashboard-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON>o', sans-serif;
}

.chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 1.2em;
  font-weight: 600;
}

.status-bar {
  background: #f8f9fa !important;
  border: 1px solid #dee2e6;
}

.tab-navigation button:hover {
  background-color: #0056b3 !important;
  color: white !important;
}

.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Responsive design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }
  
  .status-bar > div {
    flex-direction: column !important;
    gap: 10px;
  }
  
  .tab-navigation {
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .tab-navigation button {
    margin-right: 5px !important;
    padding: 8px 15px !important;
  }
}

/* Loading and error states */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 18px;
  color: #666;
}

.error-container {
  background: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  margin: 20px 0;
}

/* Custom tooltip styles */
.custom-tooltip {
  background: white !important;
  border: 1px solid #ccc !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
}

/* Chart legend styles */
.recharts-legend-wrapper {
  padding-top: 10px !important;
}

/* Status indicator styles */
.status-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.status-indicator {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
  color: white;
}

.status-normal {
  background-color: #4CAF50;
}

.status-warning {
  background-color: #FF9800;
}

.status-critical {
  background-color: #F44336;
}

/* Custom Toast Alert Styles */
.lubricant-alert-toast {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  animation: alertSlideIn 0.3s ease-out;
}

.lubricant-alert-body {
  padding: 0;
  margin: 0;
}

.lubricant-alert-progress {
  background: linear-gradient(90deg, #ff6b6b, #ffa726) !important;
}

/* Toast container positioning */
.Toastify__toast-container {
  width: 420px;
  padding: 0;
}

.Toastify__toast {
  margin-bottom: 12px;
  border-radius: 12px;
}

.Toastify__toast--error {
  background-color: transparent;
}

.Toastify__close-button {
  color: #999;
  opacity: 0.7;
}

.Toastify__close-button:hover {
  opacity: 1;
}

/* Animation for alert appearance */
@keyframes alertSlideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Pulse animation for critical alerts */
@keyframes criticalPulse {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(0,0,0,0.12), 0 0 0 1px #F4433620;
  }
  50% {
    box-shadow: 0 8px 32px rgba(244, 67, 54, 0.3), 0 0 0 3px #F4433640;
  }
}

.critical-alert {
  animation: criticalPulse 2s infinite;
}
