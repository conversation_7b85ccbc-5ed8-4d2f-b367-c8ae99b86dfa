{"ast": null, "code": "var _jsxFileName = \"D:\\\\MobioProjects\\\\iot-poc\\\\frontend\\\\src\\\\components\\\\AlertSystem.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from \"react\";\nimport { toast, ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AlertSystem = ({\n  data\n}) => {\n  _s();\n  const lastAlertTime = useRef({});\n  const ALERT_COOLDOWN = 30000; // 30 seconds cooldown between alerts for same sensor\n\n  useEffect(() => {\n    // Check for lubricant level alerts\n    const lubricantData = data.filter(item => item.sensor_type === \"lubrication_level\");\n    lubricantData.forEach(reading => {\n      if (reading.lubricant_level > 60) {\n        const now = Date.now();\n        const lastAlert = lastAlertTime.current[reading.device_id] || 0;\n\n        // Only show alert if cooldown period has passed\n        if (now - lastAlert > ALERT_COOLDOWN) {\n          showLubricantAlert(reading);\n          lastAlertTime.current[reading.device_id] = now;\n        }\n      }\n    });\n  }, [data]);\n  const showLubricantAlert = reading => {\n    const alertLevel = reading.lubricant_level > 80 ? \"CRITICAL\" : \"WARNING\";\n    const alertColor = reading.lubricant_level > 80 ? \"#F44336\" : \"#FF9800\";\n    const alertIcon = reading.lubricant_level > 80 ? \"🚨\" : \"⚠️\";\n    const AlertContent = () => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        alignItems: \"flex-start\",\n        gap: \"12px\",\n        padding: \"8px 4px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: \"28px\",\n          lineHeight: \"1\",\n          marginTop: \"2px\"\n        },\n        children: alertIcon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: \"bold\",\n            fontSize: \"16px\",\n            color: alertColor,\n            marginBottom: \"6px\",\n            lineHeight: \"1.2\"\n          },\n          children: [alertLevel, \" ALERT\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: \"15px\",\n            fontWeight: \"600\",\n            color: \"#333\",\n            marginBottom: \"4px\"\n          },\n          children: \"High Lubricant Level Detected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: \"14px\",\n            color: \"#555\",\n            marginBottom: \"6px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: reading.device_id\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), \": \", reading.lubricant_level, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: \"12px\",\n            color: \"#777\",\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\uD83D\\uDCCD \", reading.location]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: alertColor,\n              color: \"white\",\n              padding: \"2px 6px\",\n              borderRadius: \"10px\",\n              fontSize: \"11px\",\n              fontWeight: \"bold\"\n            },\n            children: \"Threshold: 60%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this);\n    toast.error(/*#__PURE__*/_jsxDEV(AlertContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 17\n    }, this), {\n      position: \"top-right\",\n      autoClose: reading.lubricant_level > 80 ? false : 10000,\n      hideProgressBar: false,\n      closeOnClick: true,\n      pauseOnHover: true,\n      draggable: true,\n      className: `lubricant-alert-toast ${reading.lubricant_level > 80 ? \"critical-alert\" : \"\"}`,\n      bodyClassName: \"lubricant-alert-body\",\n      progressClassName: \"lubricant-alert-progress\",\n      style: {\n        backgroundColor: \"#ffffff\",\n        border: `3px solid ${alertColor}`,\n        borderRadius: \"12px\",\n        boxShadow: reading.lubricant_level > 80 ? `0 8px 32px rgba(244, 67, 54, 0.2), 0 0 0 1px ${alertColor}30` : `0 8px 32px rgba(0,0,0,0.12), 0 0 0 1px ${alertColor}20`,\n        minHeight: \"80px\",\n        padding: \"0\"\n      },\n      toastId: `lubricant-${reading.device_id}-${Math.floor(reading.lubricant_level / 10)}`\n    });\n\n    // Also log to console for debugging\n    console.warn(`LUBRICANT ALERT: ${reading.device_id} - Level: ${reading.lubricant_level}%`);\n  };\n  return /*#__PURE__*/_jsxDEV(ToastContainer, {\n    position: \"top-right\",\n    autoClose: 5000,\n    hideProgressBar: false,\n    newestOnTop: true,\n    closeOnClick: true,\n    rtl: false,\n    pauseOnFocusLoss: true,\n    draggable: true,\n    pauseOnHover: true,\n    theme: \"light\"\n    // style={{ zIndex: 9999 }}\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(AlertSystem, \"wLf56FWrluCwhLozTPTtyk4zKgE=\");\n_c = AlertSystem;\nexport default AlertSystem;\nvar _c;\n$RefreshReg$(_c, \"AlertSystem\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "toast", "ToastContainer", "jsxDEV", "_jsxDEV", "AlertSystem", "data", "_s", "lastAlertTime", "ALERT_COOLDOWN", "lubricantData", "filter", "item", "sensor_type", "for<PERSON>ach", "reading", "lubricant_level", "now", "Date", "<PERSON><PERSON><PERSON><PERSON>", "current", "device_id", "showLubricantAlert", "alertLevel", "alertColor", "alertIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "display", "alignItems", "gap", "padding", "children", "fontSize", "lineHeight", "marginTop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "fontWeight", "color", "marginBottom", "justifyContent", "location", "backgroundColor", "borderRadius", "error", "position", "autoClose", "hideProgressBar", "closeOnClick", "pauseOnHover", "draggable", "className", "bodyClassName", "progressClassName", "border", "boxShadow", "minHeight", "toastId", "Math", "floor", "console", "warn", "newestOnTop", "rtl", "pauseOnFocusLoss", "theme", "_c", "$RefreshReg$"], "sources": ["D:/MobioProjects/iot-poc/frontend/src/components/AlertSystem.js"], "sourcesContent": ["import React, { useEffect, useRef } from \"react\";\nimport { toast, ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\n\nconst AlertSystem = ({ data }) => {\n  const lastAlertTime = useRef({});\n  const ALERT_COOLDOWN = 30000; // 30 seconds cooldown between alerts for same sensor\n\n  useEffect(() => {\n    // Check for lubricant level alerts\n    const lubricantData = data.filter(\n      (item) => item.sensor_type === \"lubrication_level\"\n    );\n\n    lubricantData.forEach((reading) => {\n      if (reading.lubricant_level > 60) {\n        const now = Date.now();\n        const lastAlert = lastAlertTime.current[reading.device_id] || 0;\n\n        // Only show alert if cooldown period has passed\n        if (now - lastAlert > ALERT_COOLDOWN) {\n          showLubricantAlert(reading);\n          lastAlertTime.current[reading.device_id] = now;\n        }\n      }\n    });\n  }, [data]);\n\n  const showLubricantAlert = (reading) => {\n    const alertLevel = reading.lubricant_level > 80 ? \"CRITICAL\" : \"WARNING\";\n    const alertColor = reading.lubricant_level > 80 ? \"#F44336\" : \"#FF9800\";\n    const alertIcon = reading.lubricant_level > 80 ? \"🚨\" : \"⚠️\";\n\n    const AlertContent = () => (\n      <div\n        style={{\n          display: \"flex\",\n          alignItems: \"flex-start\",\n          gap: \"12px\",\n          padding: \"8px 4px\",\n        }}\n      >\n        <div\n          style={{\n            fontSize: \"28px\",\n            lineHeight: \"1\",\n            marginTop: \"2px\",\n          }}\n        >\n          {alertIcon}\n        </div>\n        <div style={{ flex: 1 }}>\n          <div\n            style={{\n              fontWeight: \"bold\",\n              fontSize: \"16px\",\n              color: alertColor,\n              marginBottom: \"6px\",\n              lineHeight: \"1.2\",\n            }}\n          >\n            {alertLevel} ALERT\n          </div>\n          <div\n            style={{\n              fontSize: \"15px\",\n              fontWeight: \"600\",\n              color: \"#333\",\n              marginBottom: \"4px\",\n            }}\n          >\n            High Lubricant Level Detected\n          </div>\n          <div\n            style={{\n              fontSize: \"14px\",\n              color: \"#555\",\n              marginBottom: \"6px\",\n            }}\n          >\n            <strong>{reading.device_id}</strong>: {reading.lubricant_level}%\n          </div>\n          <div\n            style={{\n              fontSize: \"12px\",\n              color: \"#777\",\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n            }}\n          >\n            <span>📍 {reading.location}</span>\n            <span\n              style={{\n                backgroundColor: alertColor,\n                color: \"white\",\n                padding: \"2px 6px\",\n                borderRadius: \"10px\",\n                fontSize: \"11px\",\n                fontWeight: \"bold\",\n              }}\n            >\n              Threshold: 60%\n            </span>\n          </div>\n        </div>\n      </div>\n    );\n\n    toast.error(<AlertContent />, {\n      position: \"top-right\",\n      autoClose: reading.lubricant_level > 80 ? false : 10000,\n      hideProgressBar: false,\n      closeOnClick: true,\n      pauseOnHover: true,\n      draggable: true,\n      className: `lubricant-alert-toast ${\n        reading.lubricant_level > 80 ? \"critical-alert\" : \"\"\n      }`,\n      bodyClassName: \"lubricant-alert-body\",\n      progressClassName: \"lubricant-alert-progress\",\n      style: {\n        backgroundColor: \"#ffffff\",\n        border: `3px solid ${alertColor}`,\n        borderRadius: \"12px\",\n        boxShadow:\n          reading.lubricant_level > 80\n            ? `0 8px 32px rgba(244, 67, 54, 0.2), 0 0 0 1px ${alertColor}30`\n            : `0 8px 32px rgba(0,0,0,0.12), 0 0 0 1px ${alertColor}20`,\n        minHeight: \"80px\",\n        padding: \"0\",\n      },\n      toastId: `lubricant-${reading.device_id}-${Math.floor(\n        reading.lubricant_level / 10\n      )}`,\n    });\n\n    // Also log to console for debugging\n    console.warn(\n      `LUBRICANT ALERT: ${reading.device_id} - Level: ${reading.lubricant_level}%`\n    );\n  };\n\n  return (\n    <ToastContainer\n      position=\"top-right\"\n      autoClose={5000}\n      hideProgressBar={false}\n      newestOnTop={true}\n      closeOnClick\n      rtl={false}\n      pauseOnFocusLoss\n      draggable\n      pauseOnHover\n      theme=\"light\"\n      // style={{ zIndex: 9999 }}\n    />\n  );\n};\n\nexport default AlertSystem;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,KAAK,EAAEC,cAAc,QAAQ,gBAAgB;AACtD,OAAO,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAChC,MAAMC,aAAa,GAAGR,MAAM,CAAC,CAAC,CAAC,CAAC;EAChC,MAAMS,cAAc,GAAG,KAAK,CAAC,CAAC;;EAE9BV,SAAS,CAAC,MAAM;IACd;IACA,MAAMW,aAAa,GAAGJ,IAAI,CAACK,MAAM,CAC9BC,IAAI,IAAKA,IAAI,CAACC,WAAW,KAAK,mBACjC,CAAC;IAEDH,aAAa,CAACI,OAAO,CAAEC,OAAO,IAAK;MACjC,IAAIA,OAAO,CAACC,eAAe,GAAG,EAAE,EAAE;QAChC,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;QACtB,MAAME,SAAS,GAAGX,aAAa,CAACY,OAAO,CAACL,OAAO,CAACM,SAAS,CAAC,IAAI,CAAC;;QAE/D;QACA,IAAIJ,GAAG,GAAGE,SAAS,GAAGV,cAAc,EAAE;UACpCa,kBAAkB,CAACP,OAAO,CAAC;UAC3BP,aAAa,CAACY,OAAO,CAACL,OAAO,CAACM,SAAS,CAAC,GAAGJ,GAAG;QAChD;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACX,IAAI,CAAC,CAAC;EAEV,MAAMgB,kBAAkB,GAAIP,OAAO,IAAK;IACtC,MAAMQ,UAAU,GAAGR,OAAO,CAACC,eAAe,GAAG,EAAE,GAAG,UAAU,GAAG,SAAS;IACxE,MAAMQ,UAAU,GAAGT,OAAO,CAACC,eAAe,GAAG,EAAE,GAAG,SAAS,GAAG,SAAS;IACvE,MAAMS,SAAS,GAAGV,OAAO,CAACC,eAAe,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;IAE5D,MAAMU,YAAY,GAAGA,CAAA,kBACnBtB,OAAA;MACEuB,KAAK,EAAE;QACLC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,YAAY;QACxBC,GAAG,EAAE,MAAM;QACXC,OAAO,EAAE;MACX,CAAE;MAAAC,QAAA,gBAEF5B,OAAA;QACEuB,KAAK,EAAE;UACLM,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,GAAG;UACfC,SAAS,EAAE;QACb,CAAE;QAAAH,QAAA,EAEDP;MAAS;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACNnC,OAAA;QAAKuB,KAAK,EAAE;UAAEa,IAAI,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACtB5B,OAAA;UACEuB,KAAK,EAAE;YACLc,UAAU,EAAE,MAAM;YAClBR,QAAQ,EAAE,MAAM;YAChBS,KAAK,EAAElB,UAAU;YACjBmB,YAAY,EAAE,KAAK;YACnBT,UAAU,EAAE;UACd,CAAE;UAAAF,QAAA,GAEDT,UAAU,EAAC,QACd;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnC,OAAA;UACEuB,KAAK,EAAE;YACLM,QAAQ,EAAE,MAAM;YAChBQ,UAAU,EAAE,KAAK;YACjBC,KAAK,EAAE,MAAM;YACbC,YAAY,EAAE;UAChB,CAAE;UAAAX,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnC,OAAA;UACEuB,KAAK,EAAE;YACLM,QAAQ,EAAE,MAAM;YAChBS,KAAK,EAAE,MAAM;YACbC,YAAY,EAAE;UAChB,CAAE;UAAAX,QAAA,gBAEF5B,OAAA;YAAA4B,QAAA,EAASjB,OAAO,CAACM;UAAS;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,MAAE,EAACxB,OAAO,CAACC,eAAe,EAAC,GACjE;QAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnC,OAAA;UACEuB,KAAK,EAAE;YACLM,QAAQ,EAAE,MAAM;YAChBS,KAAK,EAAE,MAAM;YACbd,OAAO,EAAE,MAAM;YACfgB,cAAc,EAAE,eAAe;YAC/Bf,UAAU,EAAE;UACd,CAAE;UAAAG,QAAA,gBAEF5B,OAAA;YAAA4B,QAAA,GAAM,eAAG,EAACjB,OAAO,CAAC8B,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClCnC,OAAA;YACEuB,KAAK,EAAE;cACLmB,eAAe,EAAEtB,UAAU;cAC3BkB,KAAK,EAAE,OAAO;cACdX,OAAO,EAAE,SAAS;cAClBgB,YAAY,EAAE,MAAM;cACpBd,QAAQ,EAAE,MAAM;cAChBQ,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EACH;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IAEDtC,KAAK,CAAC+C,KAAK,cAAC5C,OAAA,CAACsB,YAAY;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAAE;MAC5BU,QAAQ,EAAE,WAAW;MACrBC,SAAS,EAAEnC,OAAO,CAACC,eAAe,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK;MACvDmC,eAAe,EAAE,KAAK;MACtBC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,yBACTxC,OAAO,CAACC,eAAe,GAAG,EAAE,GAAG,gBAAgB,GAAG,EAAE,EACpD;MACFwC,aAAa,EAAE,sBAAsB;MACrCC,iBAAiB,EAAE,0BAA0B;MAC7C9B,KAAK,EAAE;QACLmB,eAAe,EAAE,SAAS;QAC1BY,MAAM,EAAE,aAAalC,UAAU,EAAE;QACjCuB,YAAY,EAAE,MAAM;QACpBY,SAAS,EACP5C,OAAO,CAACC,eAAe,GAAG,EAAE,GACxB,gDAAgDQ,UAAU,IAAI,GAC9D,0CAA0CA,UAAU,IAAI;QAC9DoC,SAAS,EAAE,MAAM;QACjB7B,OAAO,EAAE;MACX,CAAC;MACD8B,OAAO,EAAE,aAAa9C,OAAO,CAACM,SAAS,IAAIyC,IAAI,CAACC,KAAK,CACnDhD,OAAO,CAACC,eAAe,GAAG,EAC5B,CAAC;IACH,CAAC,CAAC;;IAEF;IACAgD,OAAO,CAACC,IAAI,CACV,oBAAoBlD,OAAO,CAACM,SAAS,aAAaN,OAAO,CAACC,eAAe,GAC3E,CAAC;EACH,CAAC;EAED,oBACEZ,OAAA,CAACF,cAAc;IACb+C,QAAQ,EAAC,WAAW;IACpBC,SAAS,EAAE,IAAK;IAChBC,eAAe,EAAE,KAAM;IACvBe,WAAW,EAAE,IAAK;IAClBd,YAAY;IACZe,GAAG,EAAE,KAAM;IACXC,gBAAgB;IAChBd,SAAS;IACTD,YAAY;IACZgB,KAAK,EAAC;IACN;EAAA;IAAAjC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEN,CAAC;AAAChC,EAAA,CA1JIF,WAAW;AAAAiE,EAAA,GAAXjE,WAAW;AA4JjB,eAAeA,WAAW;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}